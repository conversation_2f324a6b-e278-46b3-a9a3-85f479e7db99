/* auto-generated by NAPI-RS */
/* eslint-disable */
export declare class BamlAudio {
  static fromUrl(url: string, mediaType?: string | undefined | null): BamlAudio
  static fromBase64(mediaType: string, base64: string): BamlAudio
  isUrl(): boolean
  asUrl(): string
  asBase64(): [string, string]
  toJSON(): any
}

export declare class BamlImage {
  static fromUrl(url: string, mediaType?: string | undefined | null): BamlImage
  static fromBase64(mediaType: string, base64: string): BamlImage
  isUrl(): boolean
  asUrl(): string
  asBase64(): [string, string]
  toJSON(): any
}

export declare class BamlPdf {
  static fromUrl(url: string, mediaType?: string | undefined | null): BamlPdf
  static fromBase64(mediaType: string, base64: string): BamlPdf
  get url(): string | null
  asUrl(): string
  isUrl(): boolean
  asBase64(): [string, string]
  toJSON(): any
}

export declare class BamlRuntime {
  static fromDirectory(directory: string, envVars: Record<string, string>): BamlRuntime
  static fromFiles(rootPath: string, files: Record<string, string>, envVars: Record<string, string | undefined | null>): BamlRuntime
  reset(rootPath: string, files: Record<string, string>, envVars: Record<string, string>): void
  createContextManager(): RuntimeContextManager
  callFunction(functionName: string, args: { [name: string]: any }, ctx: RuntimeContextManager, tb: TypeBuilder | undefined | null, cb: ClientRegistry | undefined | null, collectors: Array<Collector>, envVars: Record<string, string>): Promise<FunctionResult>
  callFunctionSync(functionName: string, args: { [name: string]: any }, ctx: RuntimeContextManager, tb: TypeBuilder | undefined | null, cb: ClientRegistry | undefined | null, collectors: Array<Collector>, envVars: Record<string, string>): FunctionResult
  streamFunction(functionName: string, args: { [name: string]: any }, cb: ((err: any, param: FunctionResult) => void) | undefined, ctx: RuntimeContextManager, tb: TypeBuilder | undefined | null, clientRegistry: ClientRegistry | undefined | null, collectors: Array<Collector>, envVars: Record<string, string>): FunctionResultStream
  streamFunctionSync(functionName: string, args: { [name: string]: any }, cb: ((err: any, param: FunctionResult) => void) | undefined, ctx: RuntimeContextManager, tb: TypeBuilder | undefined | null, clientRegistry: ClientRegistry | undefined | null, collectors: Array<Collector>, envVars: Record<string, string>): FunctionResultStream
  buildRequest(functionName: string, args: { [name: string]: any }, ctx: RuntimeContextManager, tb: TypeBuilder | undefined | null, cb: ClientRegistry | undefined | null, stream: boolean, envVars: Record<string, string>): Promise<HTTPRequest>
  buildRequestSync(functionName: string, args: { [name: string]: any }, ctx: RuntimeContextManager, tb: TypeBuilder | undefined | null, cb: ClientRegistry | undefined | null, stream: boolean, envVars: Record<string, string>): HTTPRequest
  parseLlmResponse(functionName: string, llmResponse: string, allowPartials: boolean, ctx: RuntimeContextManager, tb: TypeBuilder | undefined | null, cb: ClientRegistry | undefined | null, envVars: Record<string, string>): any
  setLogEventCallback(func?: undefined | ((err: any, param: BamlLogEvent) => void)): void
  flush(): void
  drainStats(): TraceStats
}

export declare class BamlSpan {
  static new(runtime: BamlRuntime, functionName: string, args: any, ctx: RuntimeContextManager, envVars: any): BamlSpan
  finish(result: any, ctx: RuntimeContextManager, envVars: any): any
}

export declare class BamlVideo {
  static fromUrl(url: string, mediaType?: string | undefined | null): BamlVideo
  static fromBase64(mediaType: string, base64: string): BamlVideo
  get url(): string | null
  asUrl(): string
  isUrl(): boolean
  asBase64(): [string, string]
  toJSON(): any
}

export declare class ClassBuilder {
  field(): FieldType
  property(name: string): ClassPropertyBuilder
}

export declare class ClassPropertyBuilder {
  setType(fieldType: FieldType): ClassPropertyBuilder
  alias(alias?: string | undefined | null): ClassPropertyBuilder
  description(description?: string | undefined | null): ClassPropertyBuilder
}

export declare class ClientRegistry {
  constructor()
  addLlmClient(name: string, provider: string, options: { [key: string]: any }, retryPolicy?: string | undefined | null): void
  setPrimary(primary: string): void
}

export declare class Collector {
  constructor(name?: string | undefined | null)
  get logs(): Array<FunctionLog>
  get last(): FunctionLog | null
  id(functionLogId: string): FunctionLog | null
  get usage(): Usage
  toString(): string
  static __functionSpanCount(): number
  static __printStorage(): void
}

export declare class EnumBuilder {
  value(name: string): EnumValueBuilder
  alias(alias?: string | undefined | null): EnumBuilder
  field(): FieldType
}

export declare class EnumValueBuilder {
  alias(alias?: string | undefined | null): EnumValueBuilder
  skip(skip?: boolean | undefined | null): EnumValueBuilder
  description(description?: string | undefined | null): EnumValueBuilder
}

export declare class FieldType {
  list(): FieldType
  optional(): FieldType
}

export declare class FunctionLog {
  toString(): string
  get id(): string
  get functionName(): string
  get logType(): string
  get timing(): Timing
  get usage(): Usage
  get calls(): (LLMCall | LLMStreamCall)[]
  get rawLlmResponse(): string | null
  get selectedCall(): unknown
}

export declare class FunctionResult {
  isOk(): boolean
  parsed(allowPartials: boolean): any
}

export declare class FunctionResultStream {
  onEvent(func?: ((err: any, param: FunctionResult) => void) | undefined): void
  done(rctx: RuntimeContextManager): Promise<FunctionResult>
}

export declare class HttpBody {
  raw(): ArrayBuffer
  text(): string
  json(): any
}
export type HTTPBody = HttpBody

export declare class HttpRequest {
  get id(): string
  get body(): HttpBody
  toString(): string
  get url(): string
  get method(): string
  get headers(): object
}
export type HTTPRequest = HttpRequest

export declare class HttpResponse {
  toString(): string
  get status(): number
  get headers(): object
  get body(): HttpBody
}
export type HTTPResponse = HttpResponse

export declare class LlmCall {
  get selected(): boolean
  get httpRequest(): HTTPRequest | null
  get httpResponse(): HTTPResponse | null
  get usage(): Usage | null
  get timing(): Timing
  get provider(): string
  get clientName(): string
  toString(): string
  toString(): string
}
export type LLMCall = LlmCall

export declare class LlmStreamCall {
  toString(): string
  get httpRequest(): HTTPRequest | null
  get httpResponse(): HTTPResponse | null
  get provider(): string
  get clientName(): string
  get selected(): boolean
  get usage(): Usage | null
  get timing(): StreamTiming
  sseResponses(): Array<SSEResponse> | null
  toString(): string
}
export type LLMStreamCall = LlmStreamCall

export declare class RuntimeContextManager {
  upsertTags(tags: any): void
  deepClone(): RuntimeContextManager
  contextDepth(): number
}

export declare class SseResponse {
  get text(): string
  json(): any | null
}
export type SSEResponse = SseResponse

export declare class StreamTiming {
  toString(): string
  get startTimeUtcMs(): number
  get durationMs(): number | null
}

export declare class Timing {
  toString(): string
  get startTimeUtcMs(): number
  get durationMs(): number | null
}

export declare class TraceStats {
  get failed(): number
  get started(): number
  get finalized(): number
  get submitted(): number
  get sent(): number
  get done(): number
  toJson(): string
}

export declare class TypeBuilder {
  constructor()
  getEnum(name: string): EnumBuilder
  getClass(name: string): ClassBuilder
  list(inner: FieldType): FieldType
  optional(inner: FieldType): FieldType
  string(): FieldType
  literalString(value: string): FieldType
  literalInt(value: number): FieldType
  literalBool(value: boolean): FieldType
  int(): FieldType
  float(): FieldType
  bool(): FieldType
  null(): FieldType
  map(key: FieldType, value: FieldType): FieldType
  union(types: Array<FieldType>): FieldType
  addBaml(baml: string, rt: BamlRuntime): void
  toString(): string
}

export declare class Usage {
  toString(): string
  get inputTokens(): number | null
  get outputTokens(): number | null
}

export interface BamlLogEvent {
  metadata: LogEventMetadata
  prompt?: string
  rawOutput?: string
  parsedOutput?: string
  startTime: string
}

export declare export declare function get_version(): string

export declare export declare function getLogLevel(): string

export declare export declare function invoke_runtime_cli(params: Array<string>): number

export interface LogEventMetadata {
  eventId: string
  parentId?: string
  rootEventId: string
}

export declare export declare function setLogJsonMode(useJson: boolean): void

export declare export declare function setLogLevel(level: string): void

export declare export declare function setLogMaxChunkLength(length: number): void

