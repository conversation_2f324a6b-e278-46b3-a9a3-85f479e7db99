{"name": "@boundaryml/baml-win32-x64-msvc", "version": "0.202.1", "cpu": ["x64"], "main": "baml.win32-x64-msvc.node", "files": ["baml.win32-x64-msvc.node"], "description": "BAML typescript bindings (package.json)", "keywords": ["napi-rs", "NAPI", "N-API", "Rust", "node-addon", "node-addon-api"], "author": "", "homepage": "https://github.com/BoundaryML/baml#readme", "license": "MIT", "engines": {"node": ">= 10"}, "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/BoundaryML/baml.git", "directory": "engine/language_client_typescript"}, "bugs": {"url": "https://github.com/BoundaryML/baml/issues"}, "os": ["win32"]}