{"name": "@boundaryml/baml", "version": "0.202.1", "description": "BAML typescript bindings (package.json)", "repository": {"type": "git", "url": "git+https://github.com/BoundaryML/baml.git", "directory": "engine/language_client_typescript"}, "license": "MIT", "keywords": ["napi-rs", "NAPI", "N-API", "Rust", "node-addon", "node-addon-api"], "bin": {"baml-cli": "cli.js"}, "files": ["./cli.js", "./index.d.ts", "./index.js", "./native.d.ts", "./native.js", "./async_context_vars.d.ts", "./async_context_vars.js", "./stream.d.ts", "./stream.js", "./type_builder.d.ts", "./type_builder.js", "./errors.d.ts", "./errors.js", "./safe_imports.d.ts", "./safe_imports.js", "./logging.d.ts", "./logging.js", "./browser.d.ts", "./browser.js"], "main": "./index.js", "types": "./index.d.ts", "napi": {"binaryName": "baml", "targets": ["aarch64-apple-darwin", "aarch64-unknown-linux-gnu", "aarch64-unknown-linux-musl", "x86_64-apple-darwin", "x86_64-pc-windows-msvc", "x86_64-unknown-linux-gnu", "x86_64-unknown-linux-musl"]}, "engines": {"node": ">= 10"}, "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "devDependencies": {"@biomejs/biome": "^1.7.3", "@napi-rs/cli": "3.0.0-alpha.62", "@types/jest": "^29.5.14", "@types/node": "^20.12.11", "jest": "^29.7.0", "npm-run-all2": "^6.1.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.4.5"}, "bugs": {"url": "https://github.com/BoundaryML/baml/issues"}, "homepage": "https://github.com/BoundaryML/baml#readme", "author": "", "dependencies": {"@scarf/scarf": "^1.3.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "moduleFileExtensions": ["ts", "js"], "transform": {"^.+\\.ts$": "ts-jest"}, "testMatch": ["**/__test__/**/*.test.ts"]}, "optionalDependencies": {"@boundaryml/baml-darwin-arm64": "0.202.1", "@boundaryml/baml-linux-arm64-gnu": "0.202.1", "@boundaryml/baml-linux-arm64-musl": "0.202.1", "@boundaryml/baml-darwin-x64": "0.202.1", "@boundaryml/baml-win32-x64-msvc": "0.202.1", "@boundaryml/baml-linux-x64-gnu": "0.202.1", "@boundaryml/baml-linux-x64-musl": "0.202.1"}, "scripts": {"artifacts": "mkdir -p artifacts && napi artifacts", "build": "pnpm build:napi-debug && pnpm build:ts_build && pnpm napi create-npm-dirs && pnpm artifacts", "release": "pnpm build:napi-release && pnpm build:ts_build", "build:debug": "pnpm build:napi-debug && pnpm build:ts_build", "build:napi-release": "pnpm build:napi-debug --release", "build:napi-debug": "napi build --js ./native.js --dts ./native.d.ts --platform", "build:ts_build": "tsc ./typescript_src/*.ts --outDir ./ --module nodenext --module nodenext --allowJs --declaration true --declarationMap true || true && pnpm build:ts_build_local", "build:local": "pnpm build:debug && pnpm napi create-npm-dirs && pnpm artifacts", "build:napi-debug-local": "napi build -o ./artifacts --js ./native.js --dts ./native.d.ts --platform", "build:ts_build_local": "tsc ./typescript_src/*.ts --outDir ./artifacts --module nodenext --module nodenext --allowJs --declaration true --declarationMap true || true", "test": "jest --config jest.config.js", "version": "napi version", "clean": "git clean -xdf .turbo node_modules target artifacts npm *.node", "dev.skip": "cargo watch -x 'build' -s 'pnpm build:napi-debug && pnpm build:ts_build'", "dev": "pnpm run build"}}