class DoneForNow {
    intent "done_for_now"
    message string 
}

function DetermineNextStep(
    thread: string 
) -> DoneForNow {
    client Qwen2_5_32B

    prompt #"
        {{ _.role("system") }}
        You are a helpful assistant that can help with tasks.
        
        {{ _.role("user") }}
        You are working on the following thread:
        {{ thread }}

        What should the next step be?
        {{ ctx.output_format }}
    "#
}

test HelloWorld {
    functions [DetermineNextStep]
    args {
    thread #"
        {
        "type": "user_input",
        "data": "hello!"
        }
    "#
    }
}