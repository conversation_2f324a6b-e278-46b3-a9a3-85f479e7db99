/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/

// This file was generated by BAML: please do not edit it. Instead, edit the
// BAML files and re-generate this code using: baml-cli generate
// You can install baml-cli with:
//  $ npm install @boundaryml/baml
//
/* eslint-disable */
// tslint:disable
// @ts-nocheck
// biome-ignore format: autogenerated code

import type { BamlRuntime, FunctionResult, BamlCtxManager, ClientRegistry, Image, Audio, Pdf, Video, Collector } from "@boundaryml/baml"
import { toBamlError, BamlStream, type HTTPRequest } from "@boundaryml/baml"
import type { Checked, Check, RecursivePartialNull as MovedRecursivePartialNull } from "./types"
import type { partial_types } from "./partial_types"
import type * as types from "./types"
import type {DoneForNow} from "./types"
import type TypeBuilder from "./type_builder"
import { AsyncHttpRequest, AsyncHttpStreamRequest } from "./async_request"
import { LlmResponseParser, LlmStreamParser } from "./parser"
import { DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_CTX, DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME } from "./globals"

/**
 * @deprecated Use RecursivePartialNull from 'baml_client/types' instead.
 */
export type RecursivePartialNull<T> = MovedRecursivePartialNull<T>

type BamlCallOptions = {
  tb?: TypeBuilder
  clientRegistry?: ClientRegistry
  collector?: Collector | Collector[]
  env?: Record<string, string | undefined>
}

export class BamlAsyncClient {
  private runtime: BamlRuntime
  private ctxManager: BamlCtxManager
  private streamClient: BamlStreamClient
  private httpRequest: AsyncHttpRequest
  private httpStreamRequest: AsyncHttpStreamRequest
  private llmResponseParser: LlmResponseParser
  private llmStreamParser: LlmStreamParser
  private bamlOptions: BamlCallOptions

  constructor(runtime: BamlRuntime, ctxManager: BamlCtxManager, bamlOptions?: BamlCallOptions) {
    this.runtime = runtime
    this.ctxManager = ctxManager
    this.streamClient = new BamlStreamClient(runtime, ctxManager, bamlOptions)
    this.httpRequest = new AsyncHttpRequest(runtime, ctxManager)
    this.httpStreamRequest = new AsyncHttpStreamRequest(runtime, ctxManager)
    this.llmResponseParser = new LlmResponseParser(runtime, ctxManager)
    this.llmStreamParser = new LlmStreamParser(runtime, ctxManager)
    this.bamlOptions = bamlOptions || {}
  }

  withOptions(bamlOptions: BamlCallOptions) {
    return new BamlAsyncClient(this.runtime, this.ctxManager, bamlOptions)
  }

  get stream() {
    return this.streamClient
  }

  get request() {
    return this.httpRequest
  }

  get streamRequest() {
    return this.httpStreamRequest
  }

  get parse() {
    return this.llmResponseParser
  }

  get parseStream() {
    return this.llmStreamParser
  }

  
  async DetermineNextStep(
      thread: string,
      __baml_options__?: BamlCallOptions
  ): Promise<types.DoneForNow> {
    try {
      const options = { ...this.bamlOptions, ...(__baml_options__ || {}) }
      const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
      const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      const env: Record<string, string> = Object.fromEntries(
        Object.entries(rawEnv).filter(([_, value]) => value !== undefined) as [string, string][]
      );
      const raw = await this.runtime.callFunction(
        "DetermineNextStep",
        {
          "thread": thread
        },
        this.ctxManager.cloneContext(),
        options.tb?.__tb(),
        options.clientRegistry,
        collector,
        env,
      )
      return raw.parsed(false) as types.DoneForNow
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
}

class BamlStreamClient {
  private runtime: BamlRuntime
  private ctxManager: BamlCtxManager
  private bamlOptions: BamlCallOptions

  constructor(runtime: BamlRuntime, ctxManager: BamlCtxManager, bamlOptions?: BamlCallOptions) {
    this.runtime = runtime
    this.ctxManager = ctxManager
    this.bamlOptions = bamlOptions || {}
  }

  
  DetermineNextStep(
      thread: string,
      __baml_options__?: { tb?: TypeBuilder, clientRegistry?: ClientRegistry, collector?: Collector | Collector[], env?: Record<string, string | undefined> }
  ): BamlStream<partial_types.DoneForNow, types.DoneForNow> {
    try {
      const options = { ...this.bamlOptions, ...(__baml_options__ || {}) }
      const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
      const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      const env: Record<string, string> = Object.fromEntries(
        Object.entries(rawEnv).filter(([_, value]) => value !== undefined) as [string, string][]
      );
      const raw = this.runtime.streamFunction(
        "DetermineNextStep",
        {
          "thread": thread
        },
        undefined,
        this.ctxManager.cloneContext(),
        options.tb?.__tb(),
        options.clientRegistry,
        collector,
        env,
      )
      return new BamlStream<partial_types.DoneForNow, types.DoneForNow>(
        raw,
        (a): partial_types.DoneForNow => a,
        (a): types.DoneForNow => a,
        this.ctxManager.cloneContext(),
      )
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
}

export const b = new BamlAsyncClient(DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME, DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_CTX)
