{"name": "12-factor-agents-main", "lockfileVersion": 3, "requires": true, "packages": {"node_modules/@boundaryml/baml": {"version": "0.202.1", "resolved": "https://registry.npmmirror.com/@boundaryml/baml/-/baml-0.202.1.tgz", "integrity": "sha512-C1VY5XERJJpOcBfx0lu+Dru9UaVdG9/O69yXkAbPu0ZbAu99Rod5InDreTigeDxCddj+mn1TvOry7NylDDW+xQ==", "license": "MIT", "dependencies": {"@scarf/scarf": "^1.3.0"}, "bin": {"baml-cli": "cli.js"}, "engines": {"node": ">= 10"}, "optionalDependencies": {"@boundaryml/baml-darwin-arm64": "0.202.1", "@boundaryml/baml-darwin-x64": "0.202.1", "@boundaryml/baml-linux-arm64-gnu": "0.202.1", "@boundaryml/baml-linux-arm64-musl": "0.202.1", "@boundaryml/baml-linux-x64-gnu": "0.202.1", "@boundaryml/baml-linux-x64-musl": "0.202.1", "@boundaryml/baml-win32-x64-msvc": "0.202.1"}}, "node_modules/@boundaryml/baml-win32-x64-msvc": {"version": "0.202.1", "resolved": "https://registry.npmmirror.com/@boundaryml/baml-win32-x64-msvc/-/baml-win32-x64-msvc-0.202.1.tgz", "integrity": "sha512-JIsw2VWaAuvDi5gD67QIPzzmVXYLXxy+7w4G0F1wnKMxVzf64/+ItwirrC0z/woLJTHoqFrxKJEW3PGIoJ0uDg==", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@scarf/scarf": {"version": "1.4.0", "resolved": "https://registry.npmmirror.com/@scarf/scarf/-/scarf-1.4.0.tgz", "integrity": "sha512-xxeapPiUXdZAE3che6f3xogoJPeZgig6omHEy1rIY5WVsB3H2BHNnZH+gHG6x91SCWyQCzWGsuL2Hh3ClO5/qQ==", "hasInstallScript": true, "license": "Apache-2.0"}, "node_modules/@types/node": {"version": "24.1.0", "resolved": "https://registry.npmmirror.com/@types/node/-/node-24.1.0.tgz", "integrity": "sha512-ut5FthK5moxFKH2T1CUOC6ctR67rQRvvHdFLCD2Ql6KXmMuCrjsSsRI9UsLCm9M18BMwClv4pn327UvB7eeO1w==", "dev": true, "license": "MIT", "dependencies": {"undici-types": "~7.8.0"}}, "node_modules/undici-types": {"version": "7.8.0", "resolved": "https://registry.npmmirror.com/undici-types/-/undici-types-7.8.0.tgz", "integrity": "sha512-9UJ2xGDvQ43tYyVMpuHlsgApydB8ZKfVYTsLDhXkFL/6gfkp+U8xTGdh8pMJv1SpZna0zxG1DwsKZsreLbXBxw==", "dev": true, "license": "MIT"}}}