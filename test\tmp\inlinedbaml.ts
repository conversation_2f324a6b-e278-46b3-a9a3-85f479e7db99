/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/

// This file was generated by BAML: please do not edit it. Instead, edit the
// BAML files and re-generate this code using: baml-cli generate
// You can install baml-cli with:
//  $ npm install @boundaryml/baml
//
/* eslint-disable */
// tslint:disable
// @ts-nocheck
// biome-ignore format: autogenerated code

const fileMap = {
  
  "agent.baml": "// human tools are async requests to a human\ntype HumanTools = ClarificationRequest | DoneForNow\n\nclass ClarificationRequest {\n  intent \"request_more_information\" @description(\"you can request more information from me\")\n  message string\n}\n\nclass DoneForNow {\n  intent \"done_for_now\"\n\n  message string @description(#\"\n    message to send to the user about the work that was done. \n  \"#)\n}\n\nclient<llm> Qwen3 {\n  provider openai\n  options {\n    model \"qwen2.5-32b-instruct-int4\"\n    api_key \"974fd8d1c155aa3d04b17bf253176b5e\"\n    base_url \"https://gateway.chat.sensedeal.vip/v1\"\n  }\n}\n\nfunction DetermineNextStep(\n    thread: string\n) -> HumanTools | CalculatorTools | FileSystemTools {\n    client Qwen3\n\n    // client \"openai/gpt-4o\"\n\n    // use /nothink for now because the thinking tokens (or streaming thereof) screw with baml (i think (no pun intended))\n    prompt #\"\n        {{ _.role(\"system\") }}\n\n        /nothink \n\n        You are a helpful assistant that can help with tasks.\n\n        {{ _.role(\"user\") }}\n\n        You are working on the following thread:\n\n        {{ thread }}\n\n        What should the next step be?\n\n        {{ ctx.output_format }}\n    \"#\n}\n\ntest HelloWorld {\n  functions [DetermineNextStep]\n  args {\n    thread #\"\n      <user_input>\n        hello!\n      </user_input>\n    \"#\n  }\n  @@assert(intent, {{this.intent == \"request_more_information\"}})\n}\n\ntest MathOperation {\n  functions [DetermineNextStep]\n  args {\n    thread #\"\n      <user_input>\n        can you multiply 3 and 4?\n      </user_input>\n    \"#\n  }\n  @@assert(intent, {{this.intent == \"multiply\"}})\n}\n\ntest LongMath {\n  functions [DetermineNextStep]\n  args {\n    thread #\"\n         <user_input>\n    can you multiply 3 and 4, then divide the result by 2 and then add 12 to that result?\n    </user_input>\n\n\n    <multiply>\n    a: 3\n    b: 4\n    </multiply>\n\n\n    <tool_response>\n    12\n    </tool_response>\n\n\n    <divide>\n    a: 12\n    b: 2\n    </divide>\n\n\n    <tool_response>\n    6\n    </tool_response>\n\n\n    <add>\n    a: 6\n    b: 12\n    </add>\n\n\n    <tool_response>\n    18\n    </tool_response>\n\n    \"#\n  }\n  @@assert(intent, {{this.intent == \"done_for_now\"}})\n  @@assert(answer, {{\"18\" in this.message}})\n}\n\n\n\ntest MathOperationWithClarification {\n  functions [DetermineNextStep]\n  args {\n    thread #\"\n          <user_input>\n          can you multiply 3 and fe1iiaff10\n          </user_input>\n      \"#\n  }\n  @@assert(intent, {{this.intent == \"request_more_information\"}})\n}\n\ntest MathOperationPostClarification {\n  functions [DetermineNextStep]\n  args {\n    thread #\"\n        <user_input>\n        can you multiply 3 and FD*(#F&& ?\n        </user_input>\n\n        <request_more_information>\n        message: It seems like there was a typo or mistake in your request. Could you please clarify or provide the correct numbers you would like to multiply?\n        </request_more_information>\n\n        <human_response>\n        lets try 12 instead\n        </human_response>\n      \"#\n  }\n  @@assert(intent, {{this.intent == \"multiply\"}})\n  @@assert(b, {{this.a == 3}})\n  @@assert(a, {{this.b == 12}})\n}\n\ntest FileSystemRead {\n  functions [DetermineNextStep]\n  args {\n    thread #\"\n      <user_input>\n      read the file test.txt\n      </user_input>\n    \"#\n  }\n  @@assert(intent, {{this.intent == \"read_file\"}})\n  @@assert(path, {{this.path == \"test.txt\"}})\n}\n        ",
  "clients.baml": "// Learn more about clients at https://docs.boundaryml.com/docs/snippets/clients/overview\r\n\r\nclient<llm> Qwen25_32B {\r\n  provider openai\r\n  options {\r\n    model \"qwen2.5-32b-instruct-int4\"\r\n    api_key \"974fd8d1c155aa3d04b17bf253176b5e\"\r\n    base_url \"https://gateway.chat.sensedeal.vip/v1\"\r\n  }\r\n}\r\n\r\nclient<llm> CustomGPT4o {\r\n  provider openai\r\n  options {\r\n    model \"gpt-4o\"\r\n    api_key env.OPENAI_API_KEY\r\n  }\r\n}\r\n\r\nclient<llm> CustomGPT4oMini {\r\n  provider openai\r\n  retry_policy Exponential\r\n  options {\r\n    model \"gpt-4o-mini\"\r\n    api_key env.OPENAI_API_KEY\r\n  }\r\n}\r\n\r\nclient<llm> CustomSonnet {\r\n  provider anthropic\r\n  options {\r\n    model \"claude-3-5-sonnet-20241022\"\r\n    api_key env.ANTHROPIC_API_KEY\r\n  }\r\n}\r\n\r\n\r\nclient<llm> CustomHaiku {\r\n  provider anthropic\r\n  retry_policy Constant\r\n  options {\r\n    model \"claude-3-haiku-20240307\"\r\n    api_key env.ANTHROPIC_API_KEY\r\n  }\r\n}\r\n\r\n// https://docs.boundaryml.com/docs/snippets/clients/round-robin\r\nclient<llm> CustomFast {\r\n  provider round-robin\r\n  options {\r\n    // This will alternate between the two clients\r\n    strategy [CustomGPT4oMini, CustomHaiku]\r\n  }\r\n}\r\n\r\n// https://docs.boundaryml.com/docs/snippets/clients/fallback\r\nclient<llm> OpenaiFallback {\r\n  provider fallback\r\n  options {\r\n    // This will try the clients in order until one succeeds\r\n    strategy [CustomGPT4oMini, CustomGPT4oMini]\r\n  }\r\n}\r\n\r\n// https://docs.boundaryml.com/docs/snippets/clients/retry\r\nretry_policy Constant {\r\n  max_retries 3\r\n  // Strategy is optional\r\n  strategy {\r\n    type constant_delay\r\n    delay_ms 200\r\n  }\r\n}\r\n\r\nretry_policy Exponential {\r\n  max_retries 2\r\n  // Strategy is optional\r\n  strategy {\r\n    type exponential_backoff\r\n    delay_ms 300\r\n    multiplier 1.5\r\n    max_delay_ms 10000\r\n  }\r\n}",
  "file_tools.baml": "// File system tools for reading, writing, and editing files\ntype FileSystemTools = ReadFileTool | WriteFileTool | ReplaceTool\n\nclass ReadFileTool {\n    intent \"read_file\"\n    path string @description(\"The absolute path to the file to read\")\n    offset int? @description(\"For text files, the 0-based line number to start reading from\")\n    limit int? @description(\"For text files, the maximum number of lines to read\")\n}\n\nclass WriteFileTool {\n    intent \"write_file\"\n    file_path string @description(\"The absolute path to the file to write to\")\n    content string @description(\"The content to write into the file\")\n}\n\nclass ReplaceTool {\n    intent \"replace\"\n    file_path string @description(\"The absolute path to the file to modify\")\n    old_string string @description(\"The exact literal text to replace. Must include sufficient context (3+ lines before/after)\")\n    new_string string @description(\"The exact literal text to replace old_string with\")\n    expected_replacements int? @description(\"The number of occurrences to replace. Defaults to 1\")\n}\n",
  "generators.baml": "// This helps use auto generate libraries you can use in the language of\n// your choice. You can have multiple generators if you use multiple languages.\n// Just ensure that the output_dir is different for each generator.\ngenerator target {\n    // Valid values: \"python/pydantic\", \"typescript\", \"ruby/sorbet\", \"rest/openapi\"\n    output_type \"typescript\"\n\n    // Where the generated code will be saved (relative to baml_src/)\n    output_dir \"../\"\n\n    // The version of the BAML package you have installed (e.g. same version as your baml-py or @boundaryml/baml).\n    // The BAML VSCode extension version should also match this version.\n    version \"0.202.1\"\n\n    // Valid values: \"sync\", \"async\"\n    // This controls what `b.FunctionName()` will be (sync or async).\n    default_client_mode async\n}\n",
  "tool_calculator.baml": "type CalculatorTools = AddTool | SubtractTool | MultiplyTool | DivideTool\n\n\nclass AddTool {\n    intent \"add\"\n    a int | float\n    b int | float\n}\n\nclass SubtractTool {\n    intent \"subtract\"\n    a int | float\n    b int | float\n}\n\nclass MultiplyTool {\n    intent \"multiply\"\n    a int | float\n    b int | float\n}\n\nclass DivideTool {\n    intent \"divide\"\n    a int | float\n    b int | float\n}\n\n",
}
export const getBamlFiles = () => {
    return fileMap;
}