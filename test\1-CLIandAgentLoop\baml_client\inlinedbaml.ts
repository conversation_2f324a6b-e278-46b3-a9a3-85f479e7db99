/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/

// This file was generated by BAML: please do not edit it. Instead, edit the
// BAML files and re-generate this code using: baml-cli generate
// You can install baml-cli with:
//  $ npm install @boundaryml/baml
//
/* eslint-disable */
// tslint:disable
// @ts-nocheck
// biome-ignore format: autogenerated code

const fileMap = {
  
  "agent.baml": "class DoneForNow {\n  intent \"done_for_now\"\n  message string \n}\n\nfunction DetermineNextStep(\n    thread: string \n) -> DoneForNow {\n    client \"openai/gpt-4o\"\n\n    prompt #\"\n        {{ _.role(\"system\") }}\n\n        You are a helpful assistant that can help with tasks.\n\n        {{ _.role(\"user\") }}\n\n        You are working on the following thread:\n\n        {{ thread }}\n\n        What should the next step be?\n\n        {{ ctx.output_format }}\n    \"#\n}\n\ntest HelloWorld {\n  functions [DetermineNextStep]\n  args {\n    thread #\"\n      {\n        \"type\": \"user_input\",\n        \"data\": \"hello!\"\n      }\n    \"#\n  }\n}",
  "clients.baml": "// Learn more about clients at https://docs.boundaryml.com/docs/snippets/clients/overview\r\n\r\nclient<llm> CustomGPT4o {\r\n  provider openai\r\n  options {\r\n    model \"gpt-4o\"\r\n    api_key env.OPENAI_API_KEY\r\n  }\r\n}\r\n\r\nclient<llm> CustomGPT4oMini {\r\n  provider openai\r\n  retry_policy Exponential\r\n  options {\r\n    model \"gpt-4o-mini\"\r\n    api_key env.OPENAI_API_KEY\r\n  }\r\n}\r\n\r\nclient<llm> CustomSonnet {\r\n  provider anthropic\r\n  options {\r\n    model \"claude-3-5-sonnet-20241022\"\r\n    api_key env.ANTHROPIC_API_KEY\r\n  }\r\n}\r\n\r\n\r\nclient<llm> CustomHaiku {\r\n  provider anthropic\r\n  retry_policy Constant\r\n  options {\r\n    model \"claude-3-haiku-20240307\"\r\n    api_key env.ANTHROPIC_API_KEY\r\n  }\r\n}\r\n\r\n// https://docs.boundaryml.com/docs/snippets/clients/round-robin\r\nclient<llm> CustomFast {\r\n  provider round-robin\r\n  options {\r\n    // This will alternate between the two clients\r\n    strategy [CustomGPT4oMini, CustomHaiku]\r\n  }\r\n}\r\n\r\n// https://docs.boundaryml.com/docs/snippets/clients/fallback\r\nclient<llm> OpenaiFallback {\r\n  provider fallback\r\n  options {\r\n    // This will try the clients in order until one succeeds\r\n    strategy [CustomGPT4oMini, CustomGPT4oMini]\r\n  }\r\n}\r\n\r\n// https://docs.boundaryml.com/docs/snippets/clients/retry\r\nretry_policy Constant {\r\n  max_retries 3\r\n  // Strategy is optional\r\n  strategy {\r\n    type constant_delay\r\n    delay_ms 200\r\n  }\r\n}\r\n\r\nretry_policy Exponential {\r\n  max_retries 2\r\n  // Strategy is optional\r\n  strategy {\r\n    type exponential_backoff\r\n    delay_ms 300\r\n    multiplier 1.5\r\n    max_delay_ms 10000\r\n  }\r\n}",
  "generators.baml": "// This helps use auto generate libraries you can use in the language of\n// your choice. You can have multiple generators if you use multiple languages.\n// Just ensure that the output_dir is different for each generator.\ngenerator target {\n    // Valid values: \"python/pydantic\", \"typescript\", \"ruby/sorbet\", \"rest/openapi\"\n    output_type \"typescript\"\n\n    // Where the generated code will be saved (relative to baml_src/)\n    output_dir \"../\"\n\n    // The version of the BAML package you have installed (e.g. same version as your baml-py or @boundaryml/baml).\n    // The BAML VSCode extension version should also match this version.\n    version \"0.202.1\"\n\n    // Valid values: \"sync\", \"async\"\n    // This controls what `b.FunctionName()` will be (sync or async).\n    default_client_mode async\n}\n",
}
export const getBamlFiles = () => {
    return fileMap;
}